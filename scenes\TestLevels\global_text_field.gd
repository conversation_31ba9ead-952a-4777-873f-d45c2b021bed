extends LineEdit

var screen_height = ProjectSettings.get_setting("display/window/size/viewport_height")

@export var debug = false
@onready var actual_resolution = get_window().size

var manual_pos = false
var first_y
var send_button_first_y
var is_keyboard_visible = false

func _ready():
	first_y = global_position.y
	# Store the initial position of the send button
	var send_button = %SendGlobal_message
	if send_button:
		send_button_first_y = send_button.global_position.y
		# Ensure send button can always receive input
		send_button.mouse_filter = Control.MOUSE_FILTER_STOP
		send_button.z_index = 100

	# Connect focus signals
	focus_entered.connect(_on_focus_entered)
	focus_exited.connect(_on_focus_exited)

	# Connect text submission signal (Enter key)
	text_submitted.connect(_on_text_submitted)

func reposition():
	var target_y
	var send_target_y
	var keyboard_height = DisplayServer.virtual_keyboard_get_height()

	if OS.get_name() == "Android" and keyboard_height > 0:
		is_keyboard_visible = true
		var ratio = 1.0 * screen_height / float(actual_resolution.y)
		target_y = min(global_position.y, screen_height - get_size().y - (keyboard_height * ratio) - 20)
		send_target_y = target_y
	else:
		is_keyboard_visible = false
		target_y = first_y
		send_target_y = send_button_first_y

	if debug:
		print("Text field target_y: ", target_y, " first_y: ", first_y)
		print("Send button target_y: ", send_target_y, " first_y: ", send_button_first_y)
		print("Keyboard height: ", keyboard_height)

	set_global_position(Vector2(global_position.x, target_y))
	var send_button = %SendGlobal_message
	if send_button:
		send_button.set_global_position(Vector2(send_button.global_position.x, send_target_y))
		# Ensure the send button is always on top and clickable
		send_button.z_index = 100
		send_button.mouse_filter = Control.MOUSE_FILTER_STOP

func _process(_delta):
	reposition()

func _input(event: InputEvent) -> void:
	# Handle clicking outside to unfocus
	if event is InputEventScreenTouch and event.pressed:
		if has_focus():
			var global_rect = get_global_rect()
			if not global_rect.has_point(event.position):
				release_focus()

	# Handle mouse clicks for desktop
	elif event is InputEventMouseButton and event.pressed and event.button_index == MOUSE_BUTTON_LEFT:
		if has_focus():
			var global_rect = get_global_rect()
			if not global_rect.has_point(event.position):
				release_focus()

func update_first_y():
	first_y = global_position.y
	var send_button = %SendGlobal_message
	if send_button:
		send_button_first_y = send_button.global_position.y

func _on_focus_entered():
	if debug:
		print("Text field focused")

func _on_focus_exited():
	if debug:
		print("Text field unfocused")
	# Hide virtual keyboard when unfocused
	if OS.get_name() == "Android":
		DisplayServer.virtual_keyboard_hide()

func _on_text_submitted(_text: String):
	# This will be handled by the waiting room script, but we ensure unfocus happens
	if debug:
		print("Text submitted, releasing focus")
	# Small delay to ensure the message is sent first
	await get_tree().process_frame
	release_focus()
