extends LineEdit

var screen_height = ProjectSettings.get_setting("display/window/size/viewport_height")

@export var debug = false
@onready var actual_resolution = get_window().size

var manual_pos = false
var first_y
func _ready():
	first_y = global_position.y


func reposition():
	var target_y
	if OS.get_name() == "Android" and DisplayServer.virtual_keyboard_get_height() > 0:
		var ratio = 1.0 * screen_height / float(actual_resolution.y)
		target_y = min(global_position.y, screen_height - get_size().y - (DisplayServer.virtual_keyboard_get_height() * ratio) - 20)
	else:
		target_y = first_y

	if debug:
		print(target_y, " ", first_y)
	set_global_position(Vector2(global_position.x, target_y))
	%SendGlobal_message.set_global_position(Vector2(%SendGlobal_message.global_position.x, target_y))


func _process(_delta):
	reposition()


func update_first_y():
	first_y = global_position.y


func _on_focus_entered():
	pass

func _on_focus_exited():
	pass


#func update_first_y_in_container():
	#process_mode = Node.PROCESS_MODE_DISABLED
	#await Constants.wait_frames(2)
	#update_first_y()
	#process_mode = Node.PROCESS_MODE_INHERIT
