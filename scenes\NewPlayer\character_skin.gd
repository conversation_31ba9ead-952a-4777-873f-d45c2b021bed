class_name <PERSON><PERSON><PERSON>
extends Node3D

@export var main_animation_player : AnimationPlayer

var moving_blend_path := "parameters/StateMachine/move/blend_position"

@onready var moving : bool = false : set = set_moving


@onready var move_speed : float = 0.0 : set = set_moving_speed
@onready var animation_tree: AnimationTree = $"../AnimationTree"
@onready var state_machine : AnimationNodeStateMachinePlayback = animation_tree.get("parameters/StateMachine/playback")

func _ready():
	animation_tree.active = true
	#main_animation_player["playback_default_blend_time"] = 0.1
	

@rpc("authority", "call_local")
func impact_anim():
	state_machine.travel("impact")

@rpc("authority", "call_local", "unreliable_ordered")
func set_moving(value : bool):
	moving = value
	if moving:
		state_machine.travel("move")
	else:
		state_machine.travel("idle")


@rpc("authority", "call_local")
func set_no_bomb():
	state_machine.travel("no_bomb")


@rpc("any_peer", "call_local", "unreliable_ordered")
func set_moving_speed(value : float):
	move_speed = clamp(value, 0.0, 1.0)
	animation_tree.set(moving_blend_path, move_speed)


@rpc("authority", "call_local", "unreliable_ordered")
func jump():
	state_machine.travel("jump")
	
@rpc("authority", "call_local", "unreliable_ordered")
func jump_bomb():
	state_machine.travel("jump_bomb")
	
@rpc("any_peer", "call_local", "unreliable_ordered")
func die():
	state_machine.travel("die")

@rpc("authority", "call_local", "unreliable_ordered")
func bomb_hold(value : bool):
	moving = value
	if moving:
		state_machine.travel("move_bomb")
	else:
		state_machine.travel("idle_bomb")
