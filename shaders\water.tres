[gd_resource type="VisualShader" load_steps=25 format=3 uid="uid://lp1bp7g8ma5k"]

[sub_resource type="VisualShaderNodeVectorOp" id="VisualShaderNodeVectorOp_go6w5"]
default_input_values = [0, Quaternion(0, 0, 0, 0), 1, Quaternion(0, 0, 0, 0)]
op_type = 2
operator = 2

[sub_resource type="VisualShaderNodeVectorOp" id="VisualShaderNodeVectorOp_ec2rv"]
default_input_values = [0, Quaternion(0, 0, 0, 0), 1, Quaternion(1.5, 1.25, 1.25, 1.25)]
op_type = 2
operator = 5

[sub_resource type="VisualShaderNodeFloatConstant" id="VisualShaderNodeFloatConstant_hx1dv"]
constant = 0.8

[sub_resource type="VisualShaderNodeFloatConstant" id="VisualShaderNodeFloatConstant_vnq6f"]
constant = 0.5

[sub_resource type="VisualShaderNodeProximityFade" id="VisualShaderNodeProximityFade_u4xky"]

[sub_resource type="VisualShaderNodeColorParameter" id="VisualShaderNodeColorParameter_qymlb"]
parameter_name = "ColorParameter"
default_value_enabled = true
default_value = Color(0, 0.266667, 0.533333, 1)

[sub_resource type="FastNoiseLite" id="FastNoiseLite_h8uke"]
noise_type = 2
seed = 1
fractal_type = 0
cellular_distance_function = 1

[sub_resource type="NoiseTexture2D" id="NoiseTexture2D_ktoxc"]
seamless = true
noise = SubResource("FastNoiseLite_h8uke")

[sub_resource type="VisualShaderNodeTexture" id="VisualShaderNodeTexture_yhfo0"]
texture = SubResource("NoiseTexture2D_ktoxc")

[sub_resource type="VisualShaderNodeVectorOp" id="VisualShaderNodeVectorOp_whopw"]
output_port_for_preview = 0
default_input_values = [0, Quaternion(0, 0, 0, 0), 1, Quaternion(0, 0, 0, 0)]
op_type = 2

[sub_resource type="VisualShaderNodeUVFunc" id="VisualShaderNodeUVFunc_qtglf"]
default_input_values = [1, Vector2(0.1, 0.1), 2, Vector2(0, 0)]

[sub_resource type="VisualShaderNodeInput" id="VisualShaderNodeInput_tde88"]
input_name = "time"

[sub_resource type="FastNoiseLite" id="FastNoiseLite_ddys4"]
noise_type = 2
seed = 3
fractal_type = 0
cellular_distance_function = 1

[sub_resource type="NoiseTexture2D" id="NoiseTexture2D_bfolj"]
width = 2000
height = 256
seamless = true
noise = SubResource("FastNoiseLite_ddys4")

[sub_resource type="VisualShaderNodeTexture" id="VisualShaderNodeTexture_smdjc"]
texture = SubResource("NoiseTexture2D_bfolj")

[sub_resource type="VisualShaderNodeUVFunc" id="VisualShaderNodeUVFunc_hldbw"]
default_input_values = [1, Vector2(-0.1, -0.1), 2, Vector2(0, 0)]

[sub_resource type="VisualShaderNodeInput" id="VisualShaderNodeInput_usg38"]
input_name = "time"

[sub_resource type="VisualShaderNodeInput" id="VisualShaderNodeInput_d2rwk"]
input_name = "time"

[sub_resource type="VisualShaderNodeUVFunc" id="VisualShaderNodeUVFunc_53ao8"]
default_input_values = [1, Vector2(0.1, 0.1), 2, Vector2(0, 0)]

[sub_resource type="VisualShaderNodeTexture" id="VisualShaderNodeTexture_myk2e"]
texture = SubResource("NoiseTexture2D_ktoxc")

[sub_resource type="VisualShaderNodeInput" id="VisualShaderNodeInput_srjgl"]
input_name = "vertex"

[sub_resource type="VisualShaderNodeInput" id="VisualShaderNodeInput_gga37"]
input_name = "normal"

[sub_resource type="VisualShaderNodeMultiplyAdd" id="VisualShaderNodeMultiplyAdd_0ueog"]
default_input_values = [0, Vector4(0, 0, 0, 0), 1, Vector4(1, 1, 1, 1), 2, Vector4(0, 0, 0, 0)]
op_type = 3

[sub_resource type="VisualShaderNodeVectorOp" id="VisualShaderNodeVectorOp_w10mu"]
default_input_values = [0, Vector3(0, 0, 0), 1, Vector3(0.25, 0.25, 0.25)]
operator = 2

[resource]
code = "shader_type spatial;
render_mode blend_mix, depth_draw_opaque, cull_back, diffuse_lambert, specular_schlick_ggx;

uniform sampler2D tex_vtx_4;
uniform vec4 ColorParameter : source_color = vec4(0.000000, 0.266667, 0.533333, 1.000000);
uniform sampler2D tex_frg_3;
uniform sampler2D tex_frg_7;



void vertex() {
// Input:2
	float n_out2p0 = TIME;


// UVFunc:3
	vec2 n_in3p1 = vec2(0.10000, 0.10000);
	vec2 n_out3p0 = vec2(n_out2p0) * n_in3p1 + UV;


// Texture2D:4
	vec4 n_out4p0 = texture(tex_vtx_4, n_out3p0);


// Input:6
	vec3 n_out6p0 = NORMAL;


// VectorOp:8
	vec3 n_in8p1 = vec3(0.25000, 0.25000, 0.25000);
	vec3 n_out8p0 = n_out6p0 * n_in8p1;


// Input:5
	vec3 n_out5p0 = VERTEX;


// MultiplyAdd:7
	vec4 n_out7p0 = (n_out4p0 * vec4(n_out8p0, 0.0)) + vec4(n_out5p0, 0.0);


// Output:0
	VERTEX = vec3(n_out7p0.xyz);


}

void fragment() {
// ColorParameter:2
	vec4 n_out2p0 = ColorParameter;


// Input:6
	float n_out6p0 = TIME;


// UVFunc:5
	vec2 n_in5p1 = vec2(0.10000, 0.10000);
	vec2 n_out5p0 = vec2(n_out6p0) * n_in5p1 + UV;


// Texture2D:3
	vec4 n_out3p0 = texture(tex_frg_3, n_out5p0);


// Input:9
	float n_out9p0 = TIME;


// UVFunc:8
	vec2 n_in8p1 = vec2(-0.10000, -0.10000);
	vec2 n_out8p0 = vec2(n_out9p0) * n_in8p1 + UV;


// Texture2D:7
	vec4 n_out7p0 = texture(tex_frg_7, n_out8p0);


// VectorOp:10
	vec4 n_out10p0 = n_out3p0 * n_out7p0;


// VectorOp:11
	vec4 n_in11p1 = vec4(1.50000, 1.25000, 1.25000, 1.25000);
	vec4 n_out11p0 = pow(n_out10p0, n_in11p1);


// VectorOp:4
	vec4 n_out4p0 = n_out2p0 + n_out11p0;


// FloatConstant:12
	float n_out12p0 = 0.800000;


// FloatConstant:13
	float n_out13p0 = 0.500000;


// Output:0
	ALBEDO = vec3(n_out4p0.xyz);
	ALPHA = n_out12p0;
	ROUGHNESS = n_out13p0;
	EMISSION = vec3(n_out11p0.xyz);


}
"
nodes/vertex/2/node = SubResource("VisualShaderNodeInput_d2rwk")
nodes/vertex/2/position = Vector2(-780, 220)
nodes/vertex/3/node = SubResource("VisualShaderNodeUVFunc_53ao8")
nodes/vertex/3/position = Vector2(-360, 220)
nodes/vertex/4/node = SubResource("VisualShaderNodeTexture_myk2e")
nodes/vertex/4/position = Vector2(-80, 240)
nodes/vertex/5/node = SubResource("VisualShaderNodeInput_srjgl")
nodes/vertex/5/position = Vector2(-780, 740)
nodes/vertex/6/node = SubResource("VisualShaderNodeInput_gga37")
nodes/vertex/6/position = Vector2(-760, 480)
nodes/vertex/7/node = SubResource("VisualShaderNodeMultiplyAdd_0ueog")
nodes/vertex/7/position = Vector2(160, 660)
nodes/vertex/8/node = SubResource("VisualShaderNodeVectorOp_w10mu")
nodes/vertex/8/position = Vector2(-400, 600)
nodes/vertex/connections = PackedInt32Array(2, 0, 3, 2, 3, 0, 4, 0, 4, 0, 7, 0, 5, 0, 7, 2, 6, 0, 8, 0, 8, 0, 7, 1, 7, 0, 0, 0)
nodes/fragment/2/node = SubResource("VisualShaderNodeColorParameter_qymlb")
nodes/fragment/2/position = Vector2(-580, -220)
nodes/fragment/3/node = SubResource("VisualShaderNodeTexture_yhfo0")
nodes/fragment/3/position = Vector2(-1420, -280)
nodes/fragment/4/node = SubResource("VisualShaderNodeVectorOp_whopw")
nodes/fragment/4/position = Vector2(-20, -80)
nodes/fragment/5/node = SubResource("VisualShaderNodeUVFunc_qtglf")
nodes/fragment/5/position = Vector2(-1820, -280)
nodes/fragment/6/node = SubResource("VisualShaderNodeInput_tde88")
nodes/fragment/6/position = Vector2(-2300, -200)
nodes/fragment/7/node = SubResource("VisualShaderNodeTexture_smdjc")
nodes/fragment/7/position = Vector2(-1420, 120)
nodes/fragment/8/node = SubResource("VisualShaderNodeUVFunc_hldbw")
nodes/fragment/8/position = Vector2(-1820, 120)
nodes/fragment/9/node = SubResource("VisualShaderNodeInput_usg38")
nodes/fragment/9/position = Vector2(-2300, 200)
nodes/fragment/10/node = SubResource("VisualShaderNodeVectorOp_go6w5")
nodes/fragment/10/position = Vector2(-840, 20)
nodes/fragment/11/node = SubResource("VisualShaderNodeVectorOp_ec2rv")
nodes/fragment/11/position = Vector2(-560, 80)
nodes/fragment/12/node = SubResource("VisualShaderNodeFloatConstant_hx1dv")
nodes/fragment/12/position = Vector2(-100, 400)
nodes/fragment/13/node = SubResource("VisualShaderNodeFloatConstant_vnq6f")
nodes/fragment/13/position = Vector2(-100, 500)
nodes/fragment/14/node = SubResource("VisualShaderNodeProximityFade_u4xky")
nodes/fragment/14/position = Vector2(-481.655, -356.03)
nodes/fragment/connections = PackedInt32Array(5, 0, 3, 0, 6, 0, 5, 2, 8, 0, 7, 0, 9, 0, 8, 2, 3, 0, 10, 0, 7, 0, 10, 1, 10, 0, 11, 0, 2, 0, 4, 0, 11, 0, 4, 1, 13, 0, 0, 3, 12, 0, 0, 1, 11, 0, 0, 5, 4, 0, 0, 0)
