extends CanvasLayer

signal start_server
signal connect_client

@export var hide_ui_and_connect: bool


func _ready():
	if hide_ui_and_connect:
		connect_client_emit()
	dedicated_server()

func dedicated_server()-> void:
	if OS.has_feature("dedicated_server") :
		print("dedicated_server")
		var arguments = OS.get_cmdline_args()
		if arguments.size() >= 1:
			var port : int = int(arguments[0])
			Global.port = port
		start_server_emit()
		#else:
			#print("No port argument provided.")
   

func start_server_emit() -> void:
	Global.server = true
	start_server.emit()
	get_tree().change_scene_to_file("res://scenes/TestLevels/waiting_room.tscn")


func connect_client_emit() -> void:
	Global.server = false
	get_tree().change_scene_to_file("res://scenes/TestLevels/waiting_room.tscn")
