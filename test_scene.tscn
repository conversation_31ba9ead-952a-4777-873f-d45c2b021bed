[gd_scene load_steps=7 format=3 uid="uid://olp2oohbv60s"]

[ext_resource type="Texture2D" uid="uid://28d31lkoevef" path="res://assets/texture/Dark/texture_08.png" id="1_uhqqe"]
[ext_resource type="PackedScene" uid="uid://cm26nto6fstcx" path="res://scenes/NewPlayer/Player.tscn" id="2_ia1lp"]

[sub_resource type="ProceduralSkyMaterial" id="ProceduralSkyMaterial_ia1lp"]
sky_horizon_color = Color(0.662243, 0.671743, 0.686743, 1)
ground_horizon_color = Color(0.662243, 0.671743, 0.686743, 1)

[sub_resource type="Sky" id="Sky_sasra"]
sky_material = SubResource("ProceduralSkyMaterial_ia1lp")

[sub_resource type="Environment" id="Environment_coq8p"]
background_mode = 2
sky = SubResource("Sky_sasra")
tonemap_mode = 2
glow_enabled = true

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_ia1lp"]
albedo_texture = ExtResource("1_uhqqe")
uv1_scale = Vector3(0.1, 0.1, 0.1)
uv1_triplanar = true

[node name="Node3D" type="Node3D"]

[node name="WorldEnvironment" type="WorldEnvironment" parent="."]
environment = SubResource("Environment_coq8p")

[node name="DirectionalLight3D" type="DirectionalLight3D" parent="."]
transform = Transform3D(-0.866023, -0.433016, 0.250001, 0, 0.499998, 0.866027, -0.500003, 0.749999, -0.43301, 0, 0, 0)
shadow_enabled = true
directional_shadow_max_distance = 1.0

[node name="CSGBox3D" type="CSGBox3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, -0.25, 0)
material_override = SubResource("StandardMaterial3D_ia1lp")
use_collision = true
size = Vector3(87, 0.5, 111)

[node name="Player" parent="." instance=ExtResource("2_ia1lp")]

[node name="Control" type="Control" parent="."]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 1193.0
offset_top = 38.0
offset_right = -227.0
offset_bottom = -318.0
grow_horizontal = 2
grow_vertical = 2

[node name="Panel" type="Panel" parent="Control"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
