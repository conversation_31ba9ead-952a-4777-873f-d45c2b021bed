extends Control

signal jump

# Called when the node enters the scene tree for the first time.
func _ready() -> void:
	pass # Replace with function body.


# Called every frame. 'delta' is the elapsed time since the previous frame.
func _process(delta: float) -> void:
	pass


func _on_touch_screen_button_pressed() -> void:
	Global.canDrag = false
	jump.emit()



func _on_button_2_pressed() -> void:
	print("JUMP")
	jump.emit()
