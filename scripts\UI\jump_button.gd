extends Control

var jump_button: <PERSON><PERSON>

# Called when the node enters the scene tree for the first time.
func _ready() -> void:
	# Find the jump button (it's named "Button2" in the scene)
	jump_button = get_node_or_null("jump")
	if jump_button:
		# Make sure the button can receive input even when virtual joystick is active
		jump_button.mouse_filter = Control.MOUSE_FILTER_STOP
		# Connect to the button's gui_input signal to handle touch directly
		jump_button.gui_input.connect(_on_jump_button_gui_input)


# Called every frame. 'delta' is the elapsed time since the previous frame.
func _process(_delta: float) -> void:
	pass

# Handle GUI input directly on the jump button to bypass virtual joystick interference
func _on_jump_button_gui_input(event: InputEvent) -> void:
	if event is InputEventScreenTouch and event.pressed:
		print("JUMP BUTTON WAS PRESSED")
		_trigger_jump()
		# Accept the event to prevent it from propagating further
		get_viewport().set_input_as_handled()

func _trigger_jump() -> void:
	print("JUMP")
	Input.action_press("jump")
	await get_tree().process_frame
	Input.action_release("jump")

func _on_touch_screen_button_pressed() -> void:
	Global.canDrag = false
