shader_type spatial;

uniform float curve_amount : hint_range(0.0, 20.0) = 0.5;
uniform float cloud_density : hint_range(0.0, 1.0) = 0.5;
uniform vec2 cloud_speed = vec2(0.1, 0.0);
uniform float scale_x : hint_range(0.1, 100.0) = 10.0;
uniform float scale_y : hint_range(0.1, 100.0) = 10.0;
uniform float softness : hint_range(0.0, 1.0) = 0.5;

float hash(vec2 p) {
    return fract(sin(dot(p, vec2(127.1, 311.7))) * 43758.5453);
}

float tileable_noise(vec2 p, float scale) {
    vec2 i = floor(p);
    vec2 f = fract(p);

    float a = hash(mod(i, scale));
    float b = hash(mod(i + vec2(1.0, 0.0), scale));
    float c = hash(mod(i + vec2(0.0, 1.0), scale));
    float d = hash(mod(i + vec2(1.0, 1.0), scale));

    vec2 u = f * f * (3.0 - 2.0 * f);
    return mix(mix(a, b, u.x), mix(c, d, u.x), u.y);
}

float fbm(vec2 p) {
    float value = 0.0;
    float amplitude = 0.5;
    float frequency = 1.0;

    for (int i = 0; i < 5; i++) {
        value += amplitude * tileable_noise(p * frequency, 10.0);
        frequency *= 2.0;
        amplitude *= 0.5;
    }

    return value;
}

void vertex() {
    // فاصله از مرکز صفحه
    float distance = length(VERTEX.xz);

    // ایجاد انحنا در تمام جهات (شکل کاسه‌ای)
    VERTEX.y -= curve_amount * distance * distance;
}

void fragment() {
    vec2 uv = UV * vec2(scale_x, scale_y) + TIME * cloud_speed;
    float n = fbm(uv);
    n = smoothstep(cloud_density - softness, cloud_density + softness, n);

    // Set the cloud color to white
    ALBEDO = vec3(3.0);

    // Make the cloud non-reflective (matte)
    ROUGHNESS = 1.0;

    // Control the transparency based on the noise
    ALPHA = n;
}